/* global process */

import axios from "axios";
import qs from "qs";
import { getStoredTokens } from "../../utils/authUtils";
// import { getAuthHeader } from "../../utils/authUtils";

const CAMPAIGN_API_URL = process.env.REACT_APP_BASE_URL;

const CAMPAIGN_API_URL_V2 = process.env.REACT_APP_WEB_SOCKET_BASE_URL;

const getAuthHeader = () => {
  const tokens = getStoredTokens();
  return tokens?.token ? `Bearer ${tokens.token}` : "";
};

const createCampaign = (data: any) => {
  return axios({
    url: `${CAMPAIGN_API_URL_V2}/Campaigns/campaign`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: JSON.stringify(data),
  });
};

const getCampaign = (data: any) => {
  return axios({
    url: `${CAMPAIGN_API_URL}/Campaign/CampaignMessage_Counts?BusinessId=${data?.businessId}&UserId=${data?.userId}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

const getCampaignByDetails = (data: any) => {
  return axios({
    url: `${CAMPAIGN_API_URL_V2}/Campaigns/campaign/${data?.campaignId}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

const getScheduledCampaigns = (data: any) => {
  return axios({
    url: `${CAMPAIGN_API_URL}/Campaigns/schedule-campaign?page=${data?.page}&pageSize=${data?.perPage}`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data?.filters,
  });
};

const uploadCampaignMedia = (data: any) => {
  const formData = new FormData();
  formData.append("File", data?.mediaFile);
  return axios({
    url: `${CAMPAIGN_API_URL}/Template/uploadFile`,
    method: "POST",
    headers: {
      "Content-Type": "multipart/form-data",
      Authorization: getAuthHeader(),
    },
    data: formData,
  });
};

const updateCampaign = (campaignId: any, data1: any) => {
  return axios({
    // url: `${CAMPAIGN_API_URL}/Campaign/Edit/${campaignId}`,
    url: `${CAMPAIGN_API_URL}/Campaign/EditCampaign`,
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: JSON.stringify(data1),
  });
};

export const filtersByIdCampaigns = (data: any) => {
  return axios({
    // url: `${CAMPAIGN_API_URL}/Campaign/CampaignDetail_filters?BusinessId=${data?.businessId}&UserId=${data?.userId}&page=${data?.page}&perPage=${data?.perPage}`,
    url: `${CAMPAIGN_API_URL}/Campaigns/campaignAllAnalytics?page=${data?.page}&pageSize=${data?.perPage}`,

    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data?.filters,
  });
};

export const deleteScheduleCampaign = (data: any) => {
  return axios({
    url: `${CAMPAIGN_API_URL}/Campaigns/campaign/${data?.campaignId}`,
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

const checkCampaignTitle = (data: any) => {
  return axios({
    url: `${CAMPAIGN_API_URL}/Campaigns/exist-campaign/${data?.businessId}/${data?.campaignTitle}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

const campaignCount = (data: any) => {
  return axios({
    url: `${CAMPAIGN_API_URL}/Campaigns/campaign-count/${data?.status}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    params: data?.filters,
    paramsSerializer: (params) => {
      // Use qs library to serialize params with dot notation
      return qs.stringify(params, { encode: false, allowDots: true });
      return qs.stringify(params, { encode: false, allowDots: true });
    },
  });
};

const createAutoReplyMessage = (data: any) => {
  return axios({
    url: `${CAMPAIGN_API_URL}/AutoReplyMessage/set-automation-campaign`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    // params: { isAutomation: true },
    data: JSON.stringify(data),
  });
};

const createBlobId = (data: any) => {
  const formDataPayload = new FormData();
  formDataPayload.append("File", data.File, data.fileName);
  formDataPayload.append("ModuleType", data.module);

  return axios({
    url: `${CAMPAIGN_API_URL}/BlobStorage/upload`,
    method: "POST",
    headers: {
      "Content-Type": "multipart/form-data",
      Authorization: getAuthHeader(),
    },
    data: formDataPayload,
  });
};

const getFileByBlobId = (data: any) => {
  return axios({
    url: `${CAMPAIGN_API_URL}/BlobStorage/get/${data}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};
const getCampaignReport = (data: any) => {
  return axios({
    url: `${CAMPAIGN_API_URL}/Campaigns/export-campaign/${data.campaignId}`,
    method: "GET",
    headers: {
      Authorization: getAuthHeader(),
    },
    responseType: "blob",
  });
};

const getCampaignById = (data: any) => {
  return axios({
    url: `${CAMPAIGN_API_URL}/Campaigns/campaign-analytics/${data?.campaignId}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

//this api is to get all the contacts for the scheduled campaign
const getContactsById = (data: any) => {
  return axios({
    url: `${CAMPAIGN_API_URL}/getContactsByIds`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data,
  });
};

const rerunCampaign = (data: any) => {
  // Use the date as a query parameter
  const formattedDate = data?.scheduledDate;

  return axios({
    url: `${CAMPAIGN_API_URL_V2}/Campaigns/rerunCampaign${
      formattedDate ? `?scheduledDate=${formattedDate}` : ""
    }`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: JSON.stringify(data?.data),
  });
};

const blobStorageByUploadedFilesId = (data: any) => {
  return axios({
    url: `${CAMPAIGN_API_URL_V2}/api/BlobStorage/get/${data?.uploadedFilesId}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

export const CAMPAIGN_API = {
  createCampaign,
  getCampaign,
  getCampaignByDetails,
  updateCampaign,
  uploadCampaignMedia,
  filtersByIdCampaigns,
  createBlobId,
  getScheduledCampaigns,
  deleteScheduleCampaign,
  checkCampaignTitle,
  createAutoReplyMessage,
  campaignCount,
  getFileByBlobId,
  getCampaignReport,
  getCampaignById,
  getContactsById,
  rerunCampaign,
  blobStorageByUploadedFilesId,
};
