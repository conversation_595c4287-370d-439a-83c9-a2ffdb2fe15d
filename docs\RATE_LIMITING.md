# SignalR Rate Limiting Implementation

## Overview

This document describes the rate limiting mechanism implemented in the Home component to prevent excessive SignalR message processing during high-volume campaigns.

## Problem Statement

When campaigns are run, a large number of SignalR calls are made to update contact messages through the `handleNewMessage` function. This can overwhelm the system and cause performance issues.

## Solution

A rate limiting mechanism has been implemented with the following specifications:

- **Rate Limit Window**: 5 seconds
- **Maximum Calls**: 100 calls per window
- **Block Duration**: 10 minutes when limit is exceeded
- **Recovery**: Automatic resumption after block period expires

## Implementation Details

### State Management

The rate limiting uses a React state object:

```typescript
const [rateLimitState, setRateLimitState] = useState({
  callTimestamps: [] as number[],
  isBlocked: boolean,
  blockStartTime: number | null,
});
```

### Rate Limiting Logic

1. **Call Tracking**: Each SignalR message call timestamp is recorded
2. **Window Management**: Timestamps older than 5 seconds are automatically removed
3. **Limit Check**: If more than 100 calls occur within 5 seconds, blocking is triggered
4. **Block Management**: During the 10-minute block period, all calls are rejected
5. **Recovery**: After 10 minutes, the system automatically resumes normal operation

### Visual Feedback

When rate limiting is active, a yellow banner appears at the top of the application showing:
- Warning message about temporary pause
- Countdown timer showing remaining block time

## Code Location

The rate limiting implementation is located in:
- **File**: `src/pages/home/<USER>
- **Function**: `checkRateLimit()`
- **Integration**: `handleNewMessage()` function

## Testing

A test utility is provided at `src/utils/rateLimitTest.ts` to simulate and verify the rate limiting behavior.

### Example Usage

```typescript
import { RateLimitTester } from '../utils/rateLimitTest';

const tester = new RateLimitTester();

// Simulate 150 rapid calls (should trigger rate limiting)
await tester.simulateRapidCalls(150, 20);

// Check current state
console.log(tester.getState());

// Reset for new test
tester.reset();
```

## Monitoring

The implementation includes console logging for debugging:

- **Approaching Limit**: Logs when more than 50 calls are made in the window
- **Rate Limit Triggered**: Warns when the limit is exceeded and blocking begins
- **Block Status**: Shows remaining block time when calls are rejected
- **Recovery**: Logs when the block period expires and normal operation resumes

## Configuration

The rate limiting parameters can be adjusted by modifying the constants in the `checkRateLimit()` function:

```typescript
const RATE_LIMIT_WINDOW = 5000; // 5 seconds
const MAX_CALLS = 100;          // Maximum calls per window
const BLOCK_DURATION = 10 * 60 * 1000; // 10 minutes
```

## Benefits

1. **System Protection**: Prevents overwhelming the application during high-volume campaigns
2. **Automatic Recovery**: No manual intervention required after rate limiting
3. **User Feedback**: Clear visual indication when rate limiting is active
4. **Debugging Support**: Comprehensive logging for troubleshooting
5. **Configurable**: Easy to adjust thresholds based on system capacity

## Future Enhancements

Potential improvements could include:

1. **Dynamic Thresholds**: Adjust limits based on system load
2. **Gradual Recovery**: Slowly increase allowed calls after block period
3. **User Controls**: Allow administrators to manually reset rate limiting
4. **Metrics Collection**: Track rate limiting events for analysis
5. **Alternative Strategies**: Implement exponential backoff or queue-based processing
