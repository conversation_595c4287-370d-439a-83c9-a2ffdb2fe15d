import { Box, FormControlLabel, Switch, Typography } from "@mui/material";
import React, { useState, useCallback, useRef, useEffect } from "react";
import ReactFlow, {
  Background,
  Controls,
  useNodesState,
  useEdgesState,
  useReactFlow,
  Node,
  Connection,
  MarkerType,
  addEdge,
  reconnectEdge,
  ConnectionLineType,
  Edge,
  Panel,
} from "reactflow";
import "reactflow/dist/style.css"; // Import styles for ReactFlow
import { bgColors } from "../../utils/bgColors";
import { v4 as uuidv4 } from "uuid";
import { useNavigate, useParams } from "react-router-dom";
import SendMessageNode from "../../components/AutomationComponents/WorkflowComponents/SendMessageNode";
import customEdge from "../../components/AutomationComponents/WorkflowComponents/CustomEdge";
import ConnectionLine from "../../components/AutomationComponents/WorkflowComponents/ConnectionLine";
import triggerWebhookNode from "../../components/AutomationComponents/WorkflowComponents/triggerWebhookNode";
import MediaTypeNode from "../../components/AutomationComponents/WorkflowComponents/MediaTypeNode";
import ReminderNode from "../../components/AutomationComponents/WorkflowComponents/ReminderNode";
import {
  MessageOutlined,
  TextsmsOutlined,
  ListAltOutlined,
  SendOutlined,
  IntegrationInstructionsOutlined,
  AccessTime as AccessTimeIcon,
} from "@mui/icons-material";
import FlowSidebar from "../../components/AutomationComponents/WorkflowComponents/FlowSidebar";
import { getSaveResponseAttribute } from "../../redux/slices/Workflows/getSaveUserResponseAttribute";
import {
  ConditionOperator,
  InteractiveType,
  NodeType,
} from "../../components/AutomationComponents/WorkflowComponents/enums";
import FlowStartNode from "../../components/AutomationComponents/WorkflowComponents/FlowStartNode";
import { updateWorkflowReactflow } from "../../redux/slices/Workflows/updateWorkflowReactflowSlice";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import ConditionNode from "../../components/AutomationComponents/WorkflowComponents/ConditionNode";
import TemplateNode from "../../components/AutomationComponents/WorkflowComponents/TemplateNode";
import { getAllWorkflowsReactflow } from "../../redux/slices/Workflows/getAllWorkflowsReactflowSlice";
import { toastActions } from "../../utils/toastSlice";
import CancelDialogPopup from "../../components/common/CancelDialogPop";
import { getWorkflowReactflowById } from "../../redux/slices/Workflows/getWorkflowReactflowByIdSlice";
import { WorkflowProvider } from "../../contexts/WorkflowContext";
import { getWorkflowAllKeywords } from "../../redux/slices/Workflows/getWorkflowAllKeywordsSlice";
import { createKeywordReactflow } from "../../redux/slices/Workflows/createKeywordsReactflowSlice";
import { GetAllStatusByTenantId } from "../../redux/slices/Utility/GetAllStatusByTenantId";
import { GetAllProjectsByTenantId } from "../../redux/slices/Utility/GetAllProjectsByTenantId";
import { getFlowstartNodes } from "../../redux/slices/Workflows/getFlowstartNodesSlice";

export interface ButtonListType {
  id: string;
  companyId: string;
  userId: string;
  listName: string;
  buttonName: string;
  inputs: { title: string; description: string }[];
}

export const messages = [
  {
    id: 1,
    icon: <MessageOutlined sx={{ color: bgColors.green }} />,
    label: "Send a Message",
    description: "Send a WhatsApp message",
    value: InteractiveType.None,
    nodeType: NodeType.InteractiveMessage,
  },
  {
    id: 2,
    icon: <TextsmsOutlined sx={{ color: bgColors.green }} />,
    label: "Text Button",
    description: "Add a text button",
    value: InteractiveType.Button,
    nodeType: NodeType.InteractiveMessage,
  },
  {
    id: 3,
    icon: <ListAltOutlined sx={{ color: bgColors.green }} />,
    label: "List Button",
    description: "Create a list menu",
    value: InteractiveType.List,
    nodeType: NodeType.InteractiveMessage,
  },
  {
    id: 4,
    icon: <SendOutlined sx={{ color: bgColors.green }} />,
    label: "Send Template",
    description: "Use message template",
    value: null,
    nodeType: NodeType.Template,
  },
  {
    id: 5,
    icon: <MessageOutlined sx={{ color: bgColors.green }} />,
    label: "Media Type",
    description: "Send media with message",
    value: null,
    nodeType: NodeType.MediaType,
  },
];

export const actions = [
  {
    id: 1,
    icon: <TextsmsOutlined sx={{ color: bgColors.green }} />,
    label: "Condition",
    description: "Add conditional logic",
    value: null,
    nodeType: NodeType.Condition,
  },
  {
    id: 2,
    icon: <IntegrationInstructionsOutlined sx={{ color: bgColors.green }} />,
    label: "Trigger Webhook",
    description: "Call external API",
    value: null,
    nodeType: NodeType.HttpRequest,
  },
  {
    id: 3,
    icon: <AccessTimeIcon sx={{ color: bgColors.green }} />,
    label: "Reminder",
    description: "Send delayed reminder",
    value: null,
    nodeType: NodeType.Reminder,
    isDisabled: (nodes: Node[]) => {
      // Find the FlowStart node
      const flowStartNode = nodes.find(
        (node) => node.type === NodeType.FlowStart
      );
      if (!flowStartNode) return true;

      // Check if the trigger type is statusChange
      if (flowStartNode.data.triggerType !== "statusChange") return true;

      // Check if any of the selected statuses are Meeting Scheduled or Site Visit Scheduled
      const selectedStatuses =
        flowStartNode.data.statusChange?.map((status: any) => status.status) ||
        [];
      return !selectedStatuses.some(
        (status: string) =>
          status === "Meeting Scheduled" || status === "Site Visit Scheduled"
      );
    },
    disabledReason:
      "Reminder node is only available when Lead Status is set to 'Meeting Scheduled' or 'Site Visit Scheduled'",
  },
];

const nodeTypes = {
  [NodeType.InteractiveMessage]: SendMessageNode,
  [NodeType.HttpRequest]: triggerWebhookNode,
  [NodeType.FlowStart]: FlowStartNode,
  [NodeType.Condition]: ConditionNode,
  [NodeType.Template]: TemplateNode,
  [NodeType.MediaType]: MediaTypeNode,
  [NodeType.Reminder]: ReminderNode,
};

const edgeTypes = {
  customEdge: customEdge,
};

const WorkflowReactFlow = () => {
  const initialNodes = (): Node[] => [
    {
      id: uuidv4(),
      type: NodeType.FlowStart,
      position: { x: 100, y: 100 },
      data: {
        keywords: [],
        leadSource: [],
        statusChange: [],
        leadProject: [],
        triggerType: "keywords" as
          | "keywords"
          | "newLead"
          | "statusChange"
          | "leadProject",
        isMessagePanelOpen: true,
        isSaved: false,

        isValid: true,
      },
    },
  ];

  const initialEdges: any[] = [];
  const { id } = useParams();
  const [workflowData, setWorkflowData] = useState<any>(null);
  const userProfileSlice = useAppSelector((state: any) => state?.adminLogin);
  const userData = userProfileSlice?.data;
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [tempWorkflowName, setTempWorkflowName] = useState("");
  const [isCancelPopupOpen, setIsCancelPopupOpen] = useState(false);
  const [actionsVisible, setActionsVisible] = useState(false);
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes()); // initial nodes
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const { screenToFlowPosition, fitView, getViewport, setViewport } =
    useReactFlow();
  const isInitialLoad = useRef(true);
  const containerRef = useRef<HTMLDivElement>(null);
  const sidebarRef = useRef<HTMLDivElement>(null);

  const [isWorkflowActive, setIsWorkflowActive] = useState(true);
  const [isSavingWorkflow, setIsSavingWorkflow] = useState(false);
  const data = useAppSelector(
    (state: any) => state.getSaveResponseAttribute.getSaveResponseAttributeData
  );
  const attributesData = data?.data;

  // Add centralized node deletion handler
  const handleNodeDelete = useCallback(
    (nodeId: string) => {
      setNodes((prevNodes) => {
        const updatedNodes = prevNodes.filter((nd) => nd.id !== nodeId);

        // Remove edges connected to the deleted node
        setEdges((prevEdges) =>
          prevEdges.filter(
            (edge) => edge.source !== nodeId && edge.target !== nodeId
          )
        );

        // Fit view to the remaining nodes after a short delay
        setTimeout(() => {
          fitView({
            nodes: updatedNodes,
            duration: 800,
            padding: 0.2,
            minZoom: 0.3,
            maxZoom: 0.7,
            includeHiddenNodes: true,
          });
        }, 100);

        return updatedNodes;
      });
    },
    [setNodes, setEdges, fitView]
  );

  // Set initial viewport
  useEffect(() => {
    if (nodes.length > 0 && isInitialLoad.current) {
      isInitialLoad.current = false;
      // Set a wider initial viewport to accommodate the sidebar
      const initialViewport = {
        x: -200, // Adjust this value to move the view left/right
        y: -100, // Adjust this value to move the view up/down
        zoom: 0.7, // Adjust this value to control initial zoom level
      };
      setViewport(initialViewport, { duration: 0 });
    }
  }, [nodes, setViewport]);

  // Handle window resize with adjusted zoom levels
  useEffect(() => {
    const handleResize = () => {
      if (nodes.length > 0) {
        fitView({
          padding: 0.5, // Increased padding
          duration: 800,
          minZoom: 0.3,
          maxZoom: 0.8, // Reduced max zoom
          includeHiddenNodes: true,
        });
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [nodes, fitView]);

  // Fit view only on initial load
  useEffect(() => {
    if (nodes.length > 0 && isInitialLoad.current) {
      isInitialLoad.current = false;
      // Add a small delay to ensure nodes are rendered
      setTimeout(() => {
        fitView({
          padding: 0.3,
          duration: 800,
          minZoom: 0.3,
          maxZoom: 1.5,
          includeHiddenNodes: true,
        });
      }, 100);
    }
  }, [nodes]);

  // Add this helper function before the useEffect
  const normalizeNodes = (nodes: any[]) => {
    return nodes.map((node) => ({
      id: node.id,
      type: node.type,
      position: node.position,
      data: node.data,
    }));
  };

  // Update the transformWorkflowNode function to handle null case
  const transformWorkflowNode = (
    node: any,
    attributesData: any[]
  ): Node | null => {
    if (typeof node.data === "string") {
      node.data = JSON.parse(node.data);
    }

    if (typeof node.data === "object" && node.data !== null) {
      const isMediaTypeNode =
        node.type === 1 &&
        (node.data.interactiveMessage?.mediaType > 1 ||
          node.data.interactiveMessage?.footer) &&
        node.data.interactiveMessage?.type === 1 &&
        !node.data.interactiveMessage?.list;

      // Check if this is a template node with isScheduleReminder
      const isReminderTemplate =
        node.type === 2 && node.data.template?.isScheduleReminder === true;

      return {
        id: node.id,
        type: isMediaTypeNode
          ? NodeType.MediaType
          : isReminderTemplate
          ? NodeType.Reminder
          : mapNodeType(node.type),
        position: {
          x: node.positionX || 0,
          y: node.positionY || 0,
        },
        data: transformNodeData(node, isMediaTypeNode, attributesData),
      };
    }
    return null;
  };

  const transformNodeData = (
    node: any,
    isMediaTypeNode: boolean,
    attributesData: any[]
  ): any => {
    switch (node.type) {
      case 0: // FlowStart
        return {
          triggerType: getTriggerType(node.data.flowStart?.entryNodeType),
          leadSource: transformLeadSource(node.data.flowStart?.leadSource),
          statusChange: transformStatusChange(node.data.flowStart?.leadStatus),
          leadProject: node.data.flowStart?.leadProject,
          isSaved: true,
        };
      case 1: // InteractiveMessage or MediaType
        return isMediaTypeNode
          ? transformMediaTypeData(node, attributesData)
          : transformInteractiveMessageData(node, attributesData);
      case 2: // Template or Reminder
        // Check if this is a reminder template
        if (node.data.template?.isScheduleReminder === true) {
          return {
            templateObj: node.data.template,
            timeInMinutes: node.data.template.timeInMinutes || 1,
            reminderTime: node.data.template.timeInMinutes || 1,
            reminderUnit: "minutes",
            isSaved: true,
          };
        }
        return transformTemplateData(node);
      case 3: // HttpRequest
        return transformHttpRequestData(node);
      case 4: // Condition
        return transformConditionData(node);
      default:
        return null;
    }
  };

  const getTriggerType = (entryNodeType: number): string => {
    switch (entryNodeType) {
      case 1:
        return "newLead";
      case 2:
        return "keywords";
      case 3:
        return "statusChange";
      case 4:
        return "leadProject";
      default:
        return "keywords";
    }
  };

  const transformMediaTypeData = (node: any, attributesData: any[]): any => {
    return {
      selectedAction: {
        nodeType: NodeType.MediaType,
        value: null,
      },
      selectedMediaType: node.data.interactiveMessage?.mediaType || 1,
      message: node.data.interactiveMessage?.body,
      mediaUrl: node.data.interactiveMessage?.mediaFile || "",
      mediaCaption: node.data.interactiveMessage?.header || "",
      footer: node.data.interactiveMessage?.footer || "",
      buttons: transformButtons(node.data.interactiveMessage?.buttons),
      variables: {
        customMessageVariables: [
          transformVariables(node.data.interactiveMessage?.variables),
        ],
        webhookBodyVariables: [],
      },
      selectedVariable: null,
      showSaveUserResponse: false,
      saveResponseType: "variable",
      response: "",
      selectedUserResponse: attributesData?.find(
        (attribute: any) => attribute.id === node.attributeId
      ),
      editSelectResponseId: null,
      deleteSelectResponseId: "",
      isSaved: true,
      timeoutTimeInMinutes: node.data.interactiveMessage?.timeOut || 1,
      timeoutTime: node.data.interactiveMessage?.timeOut || 1,
      timeoutUnit: node.data.interactiveMessage?.timeOutUnit || "minutes",
      isEnableSetTimeOut:
        node.data.interactiveMessage?.isEnbaleSetTimeOut || false,
    };
  };

  const transformInteractiveMessageData = (
    node: any,
    attributesData: any[]
  ): any => {
    return {
      selectedAction: {
        nodeType: NodeType.InteractiveMessage,
        value: node.data.interactiveMessage.type,
      },
      message: node.data.interactiveMessage.body,
      selectedButtonValue: node.data.interactiveMessage.type,
      selectedMediaType: node.data.interactiveMessage?.mediaType || 2,
      headerMediaUrl: node.data.interactiveMessage?.mediaFile || "",
      headerText: node.data.interactiveMessage?.header || "",
      footer: node.data.interactiveMessage?.footer || "",
      buttons: transformButtons(node.data.interactiveMessage?.buttons),
      selectedList: node.data.interactiveMessage?.list
        ? {
            listName: node.data.interactiveMessage.list.buttonText,
            buttonName: node.data.interactiveMessage.list.sections[0]?.title,
            inputs:
              node.data.interactiveMessage.list.sections[0]?.rows?.map(
                (row: any) => ({
                  id: row.id,
                  title: row.title,
                  description: row.description,
                })
              ) || [],
          }
        : null,
      showSaveUserResponse: false,
      variables: {
        customMessageVariables: [
          transformVariables(node.data.interactiveMessage?.variables),
        ],
        webhookBodyVariables: [],
      },
      selectedVariable: null,
      saveResponseType: "variable",
      newVariableName: "",
      response: "",
      selectedUserResponse: attributesData?.find(
        (attribute: any) => attribute.id === node.attributeId
      ),
      editSelectResponseId: null,
      isSaved: true,
      timeoutTimeInMinutes: node.data.interactiveMessage?.timeOut || 1,
      timeoutTime: node.data.interactiveMessage?.timeOut || 1,
      timeoutUnit: node.data.interactiveMessage?.timeOutUnit || "minutes",
      isEnableSetTimeOut:
        node.data.interactiveMessage?.isEnbaleSetTimeOut || false,
    };
  };

  const transformTemplateData = (node: any): any => {
    return {
      templateObj: node.data.template,
      ...node.data,
      ...(node.data.template.isScheduleReminder && {
        timeInMinutes: node.data.template.timeInMinutes,
        isScheduleReminder: node.data.template.isScheduleReminder,
      }),
      isEnableSetTimeOut: node.data.template.isEnbaleSetTimeOut || false,
      timeoutTimeInMinutes: node.data.template.timeOut || 1,
      timeoutTime: node.data.template.timeOut || 1,
      timeoutUnit: node.data.template.timeOutUnit || "minutes",
      isSaved: true,
    };
  };

  const transformHttpRequestData = (node: any): any => {
    return {
      webhookTriggerUrl: node.data.httpRequest?.url,
      webhookTriggerHttpMethod: node.data.httpRequest?.method,
      webhookTriggerHeader: Object.entries({
        "Content-Type":
          node?.data?.httpRequest?.contentType || "application/json",
        ...node.data.httpRequest?.headers,
      }).map(([key, value]) => ({
        key,
        value,
      })),
      webhookTriggerBody: JSON.parse(node.data.httpRequest?.jsonBody),
      variables: {
        customMessageVariables: [],
        webhookBodyVariables:
          node.data.httpRequest?.variableValues?.map((variable: any) => ({
            veriable: variable.variable,
            value: variable.value,
            fallbackValue: variable.fallbackValue,
            type: 3,
          })) || [],
      },
      isSaved: true,
    };
  };

  const transformConditionData = (node: any): any => {
    return {
      attribute: node.data.condition?.attribute,
      conditionType: [node.data.condition?.operator],
      conditionValue: node.data.condition?.value,
      conditionButtons: node.data.condition?.buttons,
      isSaved: true,
    };
  };

  // Update the useEffect
  useEffect(() => {
    if (workflowData === undefined || workflowData === null) {
      return;
    }

    const hasNodes =
      Array.isArray(workflowData?.nodes) && workflowData.nodes.length > 0;
    const hasEdges =
      Array.isArray(workflowData?.edges) && workflowData.edges.length > 0;

    if (hasNodes) {
      try {
        // Transform nodes
        let transformedNodes =
          workflowData?.nodes
            ?.map((node: any) => transformWorkflowNode(node, attributesData))
            .filter(Boolean) || [];

        // Add FlowStart node if needed
        const hasFlowStartNode = transformedNodes.some(
          (node: any) => node.type === NodeType.FlowStart
        );
        if (!hasFlowStartNode) {
          transformedNodes = [
            {
              id: uuidv4(),
              type: NodeType.FlowStart,
              position: { x: 100, y: 100 },
              data: {
                keywords: [],
                leadSource: [],
                statusChange: [],
                triggerType: "keywords",
                isMessagePanelOpen: true,
                isSaved: false,
              },
            },
            ...transformedNodes,
          ];
        }

        // Update nodes state
        setNodes((prevNodes: any) => {
          const normalizedPrevNodes = normalizeNodes(prevNodes);
          const normalizedTransformedNodes = normalizeNodes(transformedNodes);
          if (
            JSON.stringify(normalizedPrevNodes) !==
            JSON.stringify(normalizedTransformedNodes)
          ) {
            return transformedNodes;
          }
          return prevNodes;
        });

        // Transform edges
        const transformedEdges =
          workflowData?.edges?.map((edge: any) => ({
            id: edge.id,
            source: edge.sourceNodeId,
            target: edge.targets[0].targetNodeId,
            sourceHandle: edge.sourceHandle,
            targetHandle: edge.targetHandle,
            type: edge.type,
            markerEnd: {
              type: MarkerType.ArrowClosed,
              width: 20,
              height: 20,
              color: bgColors.green,
            },
          })) || [];

        // Update edges state
        setEdges((prevEdges: any) => {
          if (JSON.stringify(prevEdges) !== JSON.stringify(transformedEdges)) {
            return transformedEdges;
          }
          return prevEdges;
        });

        // Fit view after setting nodes
        setTimeout(() => {
          fitView({
            padding: 0.2,
            duration: 800,
            minZoom: 0.3,
            maxZoom: 1.2,
            includeHiddenNodes: true,
          });
        }, 100);
      } catch (error) {
        setNodes(initialNodes);
        setEdges(initialEdges);
        setTempWorkflowName("");
      }
    } else {
      setNodes(initialNodes);
    }

    if (!hasEdges) {
      setEdges(initialEdges);
    }

    setTempWorkflowName(workflowData?.name || "");
    setIsWorkflowActive(workflowData?.isActive ?? true);
  }, [workflowData]);

  async function getWorkflowData(id: any) {
    const result = await dispatch(getWorkflowReactflowById(id));
    try {
      if (result?.meta?.requestStatus === "fulfilled") {
        setWorkflowData(result?.payload?.data);

        await fetchWorkflowAllKeywords();
      } else {
        dispatch(
          toastActions.setToaster({
            message: result?.payload?.message || "Failed to fetch workflow",
            type: "error",
          })
        );
      }
    } catch (error: any) {
      dispatch(
        toastActions.setToaster({
          message: error?.message || "Failed to fetch workflow",
          type: "error",
        })
      );
    }
  }

  useEffect(() => {
    getWorkflowData(id);
  }, [id]);

  useEffect(() => {
    dispatch(getSaveResponseAttribute());
  }, []);

  // useEffect(() => {
  //   fetchFlowstartNodes();
  // }, []);

  async function fetchWorkflowAllKeywords() {
    const response = await dispatch(getWorkflowAllKeywords(id));
    if (response.meta.requestStatus === "fulfilled") {
      // Make sure we're getting an array of keywords
      const keywordsArray = Array.isArray(response.payload?.data)
        ? response.payload?.data
        : [];
      setNodes((prevNodes: any) =>
        prevNodes.map((node: any) => {
          if (node.type === NodeType.FlowStart) {
            return {
              ...node,
              data: {
                ...node.data,
                keywords: keywordsArray,
                isSaved: node.data.isSaved,
              },
            };
          }
          return node;
        })
      );
    } else {
      dispatch(
        toastActions.setToaster({
          message:
            response?.payload?.data?.message ||
            "An error occurred while fetching the keywords",
          type: "error",
        })
      );
    }
  }

  useEffect(() => {
    const data = {
      businessId: userData?.companyId,
    };
    dispatch(getFlowstartNodes(data));
  }, []);

  const toggleArrow = () => {
    setActionsVisible((prevState) => !prevState);
  };

  const dragOutSideRef = useRef(null);

  const onDragStart = (event: React.DragEvent, value: any) => {
    dragOutSideRef.current = value;
    event.dataTransfer.effectAllowed = "move";
  };

  const onDragOver: React.DragEventHandler<HTMLDivElement> = (
    event: React.DragEvent
  ) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  };

  const onDrop: React.DragEventHandler<HTMLDivElement> = (event) => {
    event.preventDefault();
    const displayName = dragOutSideRef.current;

    if (!displayName) return;

    // Check if the drop occurred within the sidebar boundaries
    if (sidebarRef.current) {
      const sidebarRect = sidebarRef.current.getBoundingClientRect();
      if (
        event.clientX >= sidebarRect.left &&
        event.clientX <= sidebarRect.right &&
        event.clientY >= sidebarRect.top &&
        event.clientY <= sidebarRect.bottom
      ) {
        return; // Don't create a node if dropped inside the sidebar
      }
    }

    // Get initial position from cursor
    let position = screenToFlowPosition({
      x: event.clientX,
      y: event.clientY,
    });

    const selectedAction =
      messages.find((message) => message.label === displayName) ||
      actions.find((action) => action.label === displayName);

    if (selectedAction) {
      const newNode: Node = {
        id: uuidv4(),
        type: selectedAction?.nodeType ?? "default",
        position,
        data: {
          keywords: [],
          leadSource: [],
          statusChange: [],
          leadProject: [],
          triggerType: "keywords" as
            | "keywords"
            | "newLead"
            | "statusChange"
            | "leadProject",
          conditionType: [ConditionOperator.Equals],
          conditionButtons: [
            { id: uuidv4(), name: "true" },
            { id: uuidv4(), name: "false" },
          ],
          attribute: "",
          conditionValue: "",
          templateObj: null,
          selectedTemplate: null,
          selectedPath: true,
          selectedAction: selectedAction,
          isMessagePanelOpen: true,
          message: "",
          selectedButtonValue: selectedAction.value,
          buttons:
            selectedAction.value === InteractiveType.Button
              ? [
                  {
                    id: uuidv4(),
                    text: "",
                  },
                ]
              : [],
          selectedList: null,
          showSaveUserResponse: false,
          variables: {
            customMessageVariables: [] as any[],
            webhookBodyVariables: [] as any[],
          },
          saveResponseType: "variable",
          newVariableName: "",
          response: "",
          selectedUserResponse: null,
          editSelectResponseId: null,
          deleteSelectResponseId: "",
          webhookTriggerHttpMethod: "",
          webhookTriggerUrl: "www.example.com",
          webhookTriggerHeader: [
            { key: "Content-Type", value: "application/json" },
          ],
          defaultErrorResponse:
            "We are sorry. Unable to process your request at this time. Please try again later.",
          webhookTriggerBody: "{}",
          regards: "",
          // MediaType specific fields
          selectedMediaType: 1, // Default to NONE
          mediaUrl: "",
          mediaCaption: "",
          footer: "",
          // Reminder specific fields
          reminderTime: 1,
          reminderUnit: "minutes",
          timeInMinutes: 1,
          isEnableSetTimeOut: false,
          timeoutTime: 1,
          timeoutUnit: "minutes",
          timeoutTimeInMinutes: 1,
        },
      };
      setNodes((prevNodes: any) => [...prevNodes, newNode]);

      // Wait for the node to be rendered
      setTimeout(() => {
        // Get the current viewport
        const viewport = getViewport();

        // Calculate a wider view to include the sidebar
        const extendedView = {
          x: viewport.x + 100, // Adjusted to show more of the sidebar
          y: viewport.y,
          zoom: 0.7, // Set a consistent zoom level
        };

        // First set the view to include the sidebar
        setViewport(extendedView, { duration: 0 });

        // Then fit the view to the node with adjusted padding
        fitView({
          nodes: [newNode],
          duration: 800,
          padding: 0.4, // Increased padding
          minZoom: 0.3,
          maxZoom: 1.2, // Reduced max zoom
          includeHiddenNodes: true,
        });
      }, 300);
    }
  };

  const onConnect = useCallback(
    (connection: Connection) => {
      setEdges((prevEdges) => {
        // Define handle type checks
        const isTimeoutConnection =
          connection.sourceHandle?.startsWith("right-timeoutId-");
        const isNodeDirectlyConnected =
          connection.sourceHandle?.startsWith("right-nodeId-");
        const isListOrButtonConnection =
          connection.sourceHandle?.startsWith("right-listId") ||
          connection.sourceHandle?.startsWith("right-buttonId-");
        const isConditionConnection = connection.sourceHandle?.startsWith(
          "right-conditionButtonId-"
        );

        let updatedEdges = prevEdges;

        // If connecting a timeout handle, remove any existing timeout connection from the same source node
        if (isTimeoutConnection) {
          updatedEdges = updatedEdges.filter((edge) => {
            if (edge.source !== connection.source) return true;
            // Remove other timeout connections from the same source node
            return !edge.sourceHandle?.startsWith("right-timeoutId-");
          });
        }

        // If connecting a node/button/list/condition handle, remove any existing node/button/list/condition connection from the same source node
        if (isListOrButtonConnection || isConditionConnection) {
          updatedEdges = updatedEdges.filter((edge) => {
            if (edge.source !== connection.source) return true;
            // Remove other node/button/list/condition connections from the same source node
            return !edge.sourceHandle?.startsWith("right-nodeId-");
          });
        }

        if (isNodeDirectlyConnected) {
          updatedEdges = updatedEdges.filter((edge) => {
            if (edge.source !== connection.source) return true;
            // Remove other node connections from the same source node
            return (
              !edge.sourceHandle?.startsWith("right-buttonId-") &&
              !edge.sourceHandle?.startsWith("right-listId-") &&
              !edge.sourceHandle?.startsWith("right-conditionButtonId-")
            );
          });
        }

        // Remove any existing edge from the same source to the same target (regardless of handle type)
        // updatedEdges = updatedEdges.filter(
        //   (edge) =>
        //     !(
        //       edge.source === connection.source &&
        //       edge.target === connection.target
        //     )
        // );

        const newEdge = {
          ...connection,
          type: "customEdge",
          id: uuidv4(),
          markerEnd: {
            type: MarkerType.ArrowClosed,
            width: 20,
            height: 20,
            color: `${bgColors.green}`,
          },
        };

        // Create the new edges array with the new edge added
        const newEdges = addEdge(newEdge, updatedEdges);

        // After adding the edge, validate the source node
        // This is done outside the setEdges callback to avoid nested state updates
        setTimeout(() => {
          if (connection.source) {
            const sourceNode = nodes.find(
              (node) => node.id === connection.source
            );
            if (sourceNode && sourceNode.data.isValid === false) {
              // Only validate nodes that were previously marked as invalid
              const { isValid } = validateSingleNode(
                connection.source,
                newEdges
              );

              // Update the node's validation status if it's now valid
              if (isValid) {
                setNodes((prevNodes) =>
                  prevNodes.map((node) => {
                    if (node.id === connection.source) {
                      return {
                        ...node,
                        data: {
                          ...node.data,
                          isValid: true,
                        },
                      };
                    }
                    return node;
                  })
                );
              }
            }
          }
        }, 0);

        return newEdges;
      });
    },
    [nodes]
  );

  const isValidConnection = (connection: Edge | Connection) => {
    const { source, target } = connection;
    if (source === target) return false;
    return true;
  };

  const edgeReconnectSuccessfull = useRef(false);

  const onReconnectStart = () => {
    edgeReconnectSuccessfull.current = false;
  };

  const onReconnect = useCallback(
    (oldEdge: Edge, newConnection: Connection) => {
      edgeReconnectSuccessfull.current = true;
      setEdges((prevEdge: any) => {
        const newEdges = reconnectEdge(oldEdge, newConnection, prevEdge);

        // After reconnecting the edge, validate the source node
        setTimeout(() => {
          if (newConnection.source) {
            const sourceNode = nodes.find(
              (node) => node.id === newConnection.source
            );
            if (sourceNode && sourceNode.data.isValid === false) {
              // Only validate nodes that were previously marked as invalid
              const { isValid } = validateSingleNode(
                newConnection.source,
                newEdges
              );

              // Update the node's validation status if it's now valid
              if (isValid) {
                setNodes((prevNodes) =>
                  prevNodes.map((node) => {
                    if (node.id === newConnection.source) {
                      return {
                        ...node,
                        data: {
                          ...node.data,
                          isValid: true,
                        },
                      };
                    }
                    return node;
                  })
                );
              }
            }
          }
        }, 0);

        return newEdges;
      });
    },
    [nodes]
  );

  const onReconnectEnd = (_: MouseEvent | TouchEvent, edge: Edge) => {
    if (!edgeReconnectSuccessfull.current) {
      setEdges((prevEdges: any) =>
        prevEdges?.filter((prevEdge: any) => prevEdge?.id !== edge?.id)
      );
    }
  };

  // Add this function before createNode
  const getNodeTypeNumber = (type: string): number => {
    switch (type) {
      case NodeType.FlowStart:
        return 0;
      case NodeType.InteractiveMessage:
        return 1;
      case NodeType.Template:
        return 2;
      case NodeType.HttpRequest:
        return 3;
      case NodeType.Condition:
        return 4;
      case NodeType.MediaType:
        return 5;
      case NodeType.Reminder:
        return 6;
      default:
        return -1;
    }
  };

  // Add this function before the useEffect
  const mapNodeType = (type: number | string): string => {
    switch (Number(type)) {
      case 0:
        return NodeType.FlowStart;
      case 1:
        return NodeType.InteractiveMessage;
      case 2:
        return NodeType.Template;
      case 3:
        return NodeType.HttpRequest;
      case 4:
        return NodeType.Condition;
      case 5:
        return NodeType.MediaType;
      case 6:
        return NodeType.Reminder;
      default:
        return NodeType.FlowStart;
    }
  };

  // Helper interfaces for better type safety
  interface NodeData {
    id: string;
    type: number;
    data: any;
    positionX: number;
    positionY: number;
    attributeId?: string | null;
  }

  interface FlowStartData {
    entryNodeType: number;
    leadSource: Array<{ source: string; subSource: string[] }>;
    leadStatus: Array<{ status: string; subStatus: string[] }>;
    leadProject: string[];
  }

  interface InteractiveMessageData {
    type: number;
    mediaType: number;
    body: string;
    mediaFile?: string;
    header?: string;
    headerText?: string;
    footer?: string;
    buttons: Array<{ id: string; name: string }>;
    variables: Array<{
      variable: string;
      value?: string;
      fallbackValue: string;
    }>;
    list: any;
    timeOut: number | null;
    isEnbaleSetTimeOut: boolean;
  }

  interface HttpRequestData {
    url: string;
    method: string;
    contentType: string;
    headers: Record<string, string>;
    jsonBody: string;
    variableValues: Array<{
      variable: string;
      value: string;
      fallbackValue: string;
      type: number;
    }>;
  }

  interface TemplateData {
    templateId: string;
    templateName: string;
    bodyVariableValues: any[];
    headerValue: any;
    carouselVariables: any[];
    timeInMinutes: number;
    isScheduleReminder: boolean;
    timeOut: number | null;
    isEnbaleSetTimeOut: boolean;
  }

  interface ConditionData {
    attribute: string;
    operator: number;
    value: string;
    buttons: Array<{ id: string; text: string }>;
  }

  // Helper functions for data transformation
  const transformLeadSource = (
    leadSource: any[]
  ): FlowStartData["leadSource"] => {
    return (
      leadSource?.map((lead: any) => ({
        source: lead?.source,
        subSource: lead?.subSources || [],
      })) || []
    );
  };

  const transformStatusChange = (
    statusChange: any[]
  ): FlowStartData["leadStatus"] => {
    return (
      statusChange?.map((status: any) => ({
        status: status?.status,
        subStatus: status?.subStatus || [],
      })) || []
    );
  };

  const transformVariables = (
    variables: any
  ): { normalVariables: any[]; leadratVariables: any[] } => {
    if (!variables || !Array.isArray(variables))
      return { normalVariables: [], leadratVariables: [] };

    // Separate normal variables and leadrat variables
    const normalVariables = variables
      .filter((variable: any) => variable?.variable?.startsWith("{{"))
      .map((variable: any, index: number) => ({
        veriable: variable?.variable,
        value: variable?.value,
        fallbackValue: variable?.fallbackValue,
        type: 1,
        index: index,
      }));

    const leadratVariables = variables
      .filter((variable: any) => variable?.variable?.startsWith("#"))
      .map((variable: any, index: number) => ({
        veriable: variable?.variable,
        fallbackValue: variable?.fallbackValue,
        type: 1,
        index: index,
      }));

    // Return the structure with both arrays
    return {
      normalVariables,
      leadratVariables,
    };
  };

  const createVariables = (
    variables: any
  ): InteractiveMessageData["variables"] => {
    if (!variables?.customMessageVariables?.[0]) return [];

    const normalVars = variables.customMessageVariables[0]?.normalVariables;
    const leadratVars = variables.customMessageVariables[0]?.leadratVariables;

    if (normalVars?.length > 0) {
      return normalVars.map((variable: any) => ({
        variable: variable?.veriable,
        value: variable?.value,
        fallbackValue: variable?.fallbackValue,
      }));
    }

    if (leadratVars?.length > 0) {
      return leadratVars.map((variable: any) => ({
        variable: variable?.veriable,
        fallbackValue: variable?.fallbackValue,
      }));
    }

    return [];
  };

  const createButtons = (buttons: any[]): InteractiveMessageData["buttons"] => {
    return (
      buttons?.map((button: any) => ({
        id: button?.id,
        name: button?.text,
      })) || []
    );
  };

  const transformButtons = (buttons: any[]): any => {
    return (
      buttons?.map((button: any) => ({
        id: button?.id,
        text: button?.name,
      })) || []
    );
  };

  // Node type specific creation functions
  const createFlowStartNode = (node: any): Partial<NodeData> => {
    const triggerTypeMap: Record<string, number> = {
      keywords: 2,
      newLead: 1,
      statusChange: 3,
      leadProject: 4,
    };

    return {
      type: getNodeTypeNumber(NodeType.FlowStart),
      data: {
        flowStart: {
          entryNodeType: triggerTypeMap[node?.data?.triggerType] || 2,
          leadSource: transformLeadSource(node?.data?.leadSource),
          leadStatus: transformStatusChange(node?.data?.statusChange),
          leadProject: node?.data?.leadProject || [],
        },
      },
    };
  };

  const createInteractiveMessageNode = (
    node: any,
    isMediaType: boolean
  ): Partial<NodeData> => {
    const messageData: InteractiveMessageData = {
      type: node?.data?.selectedList
        ? 2
        : node?.data?.buttons?.length > 0
        ? 1
        : 0,
      mediaType: isMediaType ? node?.data?.selectedMediaType : 1,
      body: node?.data?.message,
      mediaFile: node?.data?.headerMediaUrl || "",
      header: node?.data?.mediaCaption || "",
      footer: node?.data?.footer || "",
      buttons: createButtons(node?.data?.buttons),
      variables: createVariables(node?.data?.variables),
      list: node?.data?.selectedList
        ? {
            buttonText: node?.data?.selectedList?.listName,
            sections: [
              {
                title: node?.data?.selectedList?.buttonName,
                rows:
                  node?.data?.selectedList?.inputs?.map((input: any) => ({
                    id: input?.id,
                    title: input?.title,
                    description: input?.description,
                  })) || [],
              },
            ],
          }
        : null,
      timeOut:
        node?.data?.isEnableSetTimeOut && node?.data?.timeoutTimeInMinutes > 0
          ? node?.data?.timeoutTimeInMinutes
          : null,
      isEnbaleSetTimeOut: node?.data?.isEnableSetTimeOut || false,
    };

    return {
      type: getNodeTypeNumber(NodeType.InteractiveMessage),
      data: {
        interactiveMessage: messageData,
      },
    };
  };

  const createHttpRequestNode = (node: any): Partial<NodeData> => {
    const headers =
      node?.data?.webhookTriggerHeader?.reduce((acc: any, header: any) => {
        if (header.key !== "Content-Type" && header.key !== "Accept") {
          acc[header.key] = header.value;
        }
        return acc;
      }, {}) || {};

    const httpRequestData: HttpRequestData = {
      url: node?.data?.webhookTriggerUrl,
      method: node?.data?.webhookTriggerHttpMethod,
      contentType:
        node?.data?.webhookTriggerHeader?.find(
          (header: any) =>
            header.key === "Content-Type" || header.key === "Accept"
        )?.value || "application/json",
      headers,
      jsonBody: JSON.stringify(node?.data?.webhookTriggerBody) || "{}",
      variableValues:
        node?.data?.variables?.webhookBodyVariables?.map((variable: any) => ({
          variable: variable?.veriable,
          value: variable?.value,
          fallbackValue: variable?.fallbackValue,
          type: 3,
        })) || [],
    };

    return {
      type: getNodeTypeNumber(NodeType.HttpRequest),
      data: {
        httpRequest: httpRequestData,
      },
    };
  };

  const createTemplateNode = (
    node: any,
    isReminder: boolean
  ): Partial<NodeData> => {
    const templateData: TemplateData = {
      templateId: node?.data?.templateObj?.templateId || "",
      templateName: node?.data?.templateObj?.templateName || "",
      bodyVariableValues:
        node?.data?.templateObj?.variables?.length > 0
          ? node?.data?.templateObj?.variables
              ?.filter((v: any) => v.type === "body")
              .map((v: any) => ({
                variable: v.id,
                value: v.field,
                fallbackValue: v.fallBackValue,
              }))
          : node?.data?.templateObj?.leadratVariables?.length > 0
          ? node?.data?.templateObj?.leadratVariables?.map((v: any) => ({
              variable: v.id,
              fallbackValue: v.fallBackValue,
            }))
          : node?.data?.templateObj?.bodyVariableValues,
      headerValue:
        node?.data?.templateObj?.variables?.length > 0
          ? node?.data?.templateObj?.variables
              ?.filter((v: any) => v.type === "header")[0]
              ?.map((v: any) => ({
                variable: v.id,
                value: v.field,
                fallbackValue: v.fallBackValue,
              }))
          : node?.data?.templateObj?.leadratVariables?.length > 0
          ? node?.data?.templateObj?.leadratVariables
              ?.filter((v: any) => v.type === "header")[0]
              ?.map((v: any) => ({
                variable: v.id,
                fallbackValue: v.fallBackValue,
              }))
          : node.data?.templateObj?.headerValue,
      carouselVariables:
        node?.data?.templateObj?.carouselBodyVariables?.length > 0
          ? node?.data?.templateObj?.carouselBodyVariables?.map(
              (carousel: any, index: number) => ({
                bodyCarouselVariableValues: carousel.map((v: any) => ({
                  value: v.field === "ContactNumber" ? "Contact" : v.field,
                  fallbackValue: v.fallBackValue,
                })),
                mediaUrl:
                  node?.data?.templateObj?.carouselCards[index]?.headerMediaUrl,
              })
            )
          : node?.data?.templateObj?.carouselLeadratBodyVariables?.length > 0
          ? node?.data?.templateObj?.carouselLeadratBodyVariables?.map(
              (carousel: any, index: number) => ({
                bodyCarouselVariableValues: carousel.map((v: any) => ({
                  value: v.field === "ContactNumber" ? "Contact" : v.field,
                  fallbackValue: v.fallBackValue,
                })),
                mediaUrl:
                  node?.data?.templateObj?.carouselCards[index]?.headerMediaUrl,
              })
            )
          : node?.data?.templateObj?.carouselVariables,
      timeInMinutes: isReminder ? node?.data?.timeInMinutes || 1 : null,
      isScheduleReminder: isReminder ? true : false,
      timeOut:
        node?.data?.isEnableSetTimeOut && node?.data?.timeoutTimeInMinutes > 0
          ? node?.data?.timeoutTimeInMinutes
          : null,
      isEnbaleSetTimeOut: node?.data?.isEnableSetTimeOut || false,
    };

    return {
      type: getNodeTypeNumber(NodeType.Template),
      data: {
        template: templateData,
      },
    };
  };

  const createConditionNode = (node: any): Partial<NodeData> => {
    const conditionData: ConditionData = {
      attribute: node?.data?.attribute,
      operator: Array.isArray(node?.data?.conditionType)
        ? Number(node?.data?.conditionType[0])
        : Number(node?.data?.conditionType),
      value: node?.data?.conditionValue,
      buttons:
        node?.data?.conditionButtons?.map((button: any) => ({
          id: button?.id,
          name: button?.name,
        })) || [],
    };

    return {
      type: getNodeTypeNumber(NodeType.Condition),
      data: {
        condition: conditionData,
      },
    };
  };

  // Main createNode function using factory pattern
  function createNode(
    node: any,
    type: NodeType,
    positionX = 0,
    positionY = 0
  ): NodeData {
    const baseNode: NodeData = {
      id: node?.id,
      type: 0,
      data: {},
      positionX,
      positionY,
      attributeId:
        type === NodeType.InteractiveMessage || type === NodeType.MediaType
          ? node?.data?.selectedUserResponse?.id
          : null,
    };

    let nodeData: Partial<NodeData> = {};

    switch (type) {
      case NodeType.FlowStart:
        nodeData = createFlowStartNode(node);
        break;
      case NodeType.InteractiveMessage:
      case NodeType.MediaType:
        nodeData = createInteractiveMessageNode(
          node,
          type === NodeType.MediaType
        );
        break;
      case NodeType.HttpRequest:
        nodeData = createHttpRequestNode(node);
        break;
      case NodeType.Template:
      case NodeType.Reminder:
        nodeData = createTemplateNode(node, type === NodeType.Reminder);
        break;
      case NodeType.Condition:
        nodeData = createConditionNode(node);
        break;
    }

    return {
      ...baseNode,
      ...nodeData,
    };
  }

  function createEdge(edge: any, nodes: any[]) {
    const sourceNode = nodes?.find((node) => node?.id === edge?.source);
    const sourceButtonId = edge?.sourceHandle.split("-").slice(2).join("-");

    const isTimeOutEdge = edge?.sourceHandle?.startsWith("right-timeoutId-");

    const buttonName = sourceNode?.data?.interactiveMessage?.buttons?.find(
      (button: any) => button?.id === sourceButtonId
    )?.name;

    const listButtonName =
      sourceNode?.data?.interactiveMessage?.list?.sections[0]?.rows?.find(
        (button: any) => button?.id === sourceButtonId
      )?.title;

    const conditionButtonName = sourceNode?.data?.condition?.buttons?.find(
      (button: any) => button?.id === sourceButtonId
    )?.name;

    // For MediaType nodes, get button name from interactiveMessage structure
    const mediaTypeButtonName =
      sourceNode?.type === NodeType.MediaType
        ? sourceNode?.data?.buttons?.find(
            (button: any) => button?.id === sourceButtonId
          )?.text
        : null;

    const listArray = sourceNode?.data?.interactiveMessage?.list;
    const result: any = {
      id: edge?.id, // Include the edge ID
      sourceNodeId: edge?.source,
      sourceHandle: edge?.sourceHandle,
      targetHandle: edge?.targetHandle,
      type: edge?.type,
      targets: [
        {
          targetNodeId: edge?.target,
          condition: isTimeOutEdge
            ? "timeout"
            : (sourceNode?.type === NodeType.InteractiveMessage ||
                sourceNode?.type === 1) &&
              sourceNode?.data?.interactiveMessage?.buttons?.length > 0
            ? `Name == ${buttonName}`
            : (sourceNode?.type === NodeType.InteractiveMessage ||
                sourceNode?.type === 1) &&
              listArray?.sections[0]?.rows?.length > 0
            ? `Name == '${listButtonName}'`
            : (sourceNode?.type === NodeType.Condition ||
                sourceNode?.type === 4) &&
              sourceNode?.data?.condition?.buttons?.length > 0 &&
              conditionButtonName !== "false"
            ? `Name == ${conditionButtonName}`
            : (sourceNode?.type === NodeType.MediaType ||
                sourceNode?.type === 5) &&
              sourceNode?.data?.buttons?.length > 0
            ? `Name == ${mediaTypeButtonName}`
            : null,
        },
      ],
    };
    return result;
  }

  function cleanObject(obj: any): any {
    if (Array.isArray(obj)) {
      const cleanedArray = obj
        .map(cleanObject)
        .filter(
          (item) =>
            item !== null &&
            item !== undefined &&
            (typeof item !== "object" || Object.keys(item).length > 0)
        );
      return cleanedArray.length ? cleanedArray : undefined;
    }

    if (typeof obj === "object" && obj !== null) {
      const cleanedObj: any = {};
      Object.entries(obj).forEach(([key, value]) => {
        const cleanedValue = cleanObject(value);
        const isEmptyObject =
          typeof cleanedValue === "object" &&
          cleanedValue &&
          !Array.isArray(cleanedValue) &&
          Object.keys(cleanedValue).length === 0;

        if (
          cleanedValue !== undefined &&
          cleanedValue !== null &&
          cleanedValue !== "" &&
          !(Array.isArray(cleanedValue) && cleanedValue.length === 0) &&
          !isEmptyObject
        ) {
          cleanedObj[key] = cleanedValue;
        }
      });
      return Object.keys(cleanedObj).length ? cleanedObj : undefined;
    }

    return obj;
  }

  // Validate a single node in the workflow
  const validateSingleNode = (nodeId: string, currentEdges: Edge[]) => {
    // Find the node to validate
    const node = nodes.find((n) => n.id === nodeId);
    if (!node) return { isValid: true, validationErrors: [] };

    // Skip validation for FlowStartNode
    if (node.type === NodeType.FlowStart) {
      return { isValid: true, validationErrors: [] };
    }

    let isValid = true;
    const validationErrors = [];

    // Validate InteractiveMessage nodes
    if (node.type === NodeType.InteractiveMessage) {
      // Check if message is empty
      if (!node.data.message) {
        validationErrors.push(
          `Interactive Message: Message content cannot be empty`
        );
        isValid = false;
      }

      // Check if custom message variables are empty and fallback value is not provided
      if (
        node.data.variables?.customMessageVariables?.normalVariables?.length > 0
      ) {
        node.data.variables?.customMessageVariables?.normalVariables?.forEach(
          (variable: any) => {
            if (!variable.value || variable.value.trim() === "") {
              validationErrors.push(
                `Interactive Message: variable value cannot be empty`
              );
              isValid = false;
            } else if (
              !variable.fallbackValue ||
              variable.fallbackValue.trim() === ""
            ) {
              validationErrors.push(
                `Interactive Message: variable fallback value cannot be empty`
              );
              isValid = false;
            }
          }
        );
      } else if (
        node.data.variables?.customMessageVariables?.leadratVariables?.length >
        0
      ) {
        node.data.variables?.customMessageVariables?.leadratVariables?.forEach(
          (variable: any) => {
            if (!variable.value || variable.value.trim() === "") {
              validationErrors.push(
                `Interactive Message: variable value cannot be empty`
              );
              isValid = false;
            } else if (
              !variable.fallbackValue ||
              variable.fallbackValue.trim() === ""
            ) {
              validationErrors.push(
                `Interactive Message: variable fallback value cannot be empty`
              );
              isValid = false;
            }
          }
        );
      }

      // Timeout path validation
      if (node.data.isEnableSetTimeOut || node.data.isEnbaleSetTimeOut) {
        // Check if there is a timeout connection from this node
        const hasTimeoutConnection = currentEdges.some(
          (edge) =>
            edge.source === node.id &&
            edge.sourceHandle &&
            edge.sourceHandle.startsWith("right-timeoutId-")
        );
        if (!hasTimeoutConnection) {
          validationErrors.push(
            "Interactive Message: Timeout path must be connected to another node."
          );
          isValid = false;
        }
      }

      // Check if buttons are empty when button type is selected
      if (
        node.data.selectedButtonValue === InteractiveType.Button &&
        node.data.buttons &&
        node.data.buttons.length > 0
      ) {
        const emptyButtons = node.data.buttons.filter(
          (button: any) => !button.text
        );
        if (emptyButtons.length > 0) {
          validationErrors.push(
            `Interactive Message: Button text cannot be empty`
          );
          isValid = false;
        }

        // Check if buttons are connected to other nodes or if the node itself is connected
        const connectedButtons = currentEdges.filter(
          (edge) =>
            edge.source === node.id &&
            edge.sourceHandle &&
            edge.sourceHandle.startsWith("right-buttonId-")
        );

        // Check if the node itself is directly connected
        const isNodeDirectlyConnected = currentEdges.some(
          (edge) =>
            edge.source === node.id &&
            edge.sourceHandle === `right-nodeId-${node.id}`
        );

        // Only validate button connections if the node itself is not connected
        if (
          connectedButtons.length < node.data.buttons.length &&
          !isNodeDirectlyConnected
        ) {
          validationErrors.push(
            `Interactive Message: Either connect all buttons to other nodes or connect this node directly`
          );
          isValid = false;
        }
      }

      // Check if list items are empty when list type is selected
      if (
        node.data.selectedButtonValue === InteractiveType.List &&
        node.data.selectedList &&
        node.data.selectedList.inputs &&
        node.data.selectedList.inputs.length > 0
      ) {
        const emptyListItems = node.data.selectedList.inputs.filter(
          (item: any) => !item.title
        );
        if (emptyListItems.length > 0) {
          validationErrors.push(
            `Interactive Message: List item title cannot be empty`
          );
          isValid = false;
        }

        // Check if list items are connected to other nodes
        const connectedListItems = currentEdges.filter(
          (edge) =>
            edge.source === node.id &&
            edge.sourceHandle &&
            edge.sourceHandle.startsWith("right-listId-")
        );

        // Check if the node itself is directly connected
        const isNodeDirectlyConnected = currentEdges.some(
          (edge) =>
            edge.source === node.id &&
            edge.sourceHandle === `right-nodeId-${node.id}`
        );

        // Only validate list item connections if the node itself is not connected
        if (
          connectedListItems.length < node.data.selectedList.inputs.length &&
          !isNodeDirectlyConnected
        ) {
          validationErrors.push(
            `Interactive Message: Either connect all list items to other nodes or connect this node directly`
          );
          isValid = false;
        }
      }
    }

    // Validate Template nodes
    if (node.type === NodeType.Template) {
      if (!node.data.templateObj || !node.data.templateObj.templateId) {
        validationErrors.push(`Template Node: A template must be selected`);
        isValid = false;
      }
      // Timeout path validation for Template nodes
      if (node.data.isEnableSetTimeOut || node.data.isEnbaleSetTimeOut) {
        // Check if there is a timeout connection from this node
        const hasTimeoutConnection = currentEdges.some(
          (edge) =>
            edge.source === node.id &&
            edge.sourceHandle &&
            edge.sourceHandle.startsWith("right-timeoutId-")
        );
        if (!hasTimeoutConnection) {
          validationErrors.push(
            "Template Node: Timeout path must be connected to another node."
          );
          isValid = false;
        }
      }
    }

    // Validate Condition nodes
    if (node.type === NodeType.Condition) {
      // Check if attribute is selected
      if (!node.data.attribute) {
        validationErrors.push(`Condition Node: An attribute must be selected`);
        isValid = false;
      }

      // Check if condition value is provided
      if (!node.data.conditionValue) {
        validationErrors.push(
          `Condition Node: A condition value must be provided`
        );
        isValid = false;
      }

      // Check if both true and false buttons are connected
      const trueButtonConnected = currentEdges.some(
        (edge) =>
          edge.source === node.id &&
          edge.sourceHandle &&
          edge.sourceHandle.includes("conditionButtonId-") &&
          node.data.conditionButtons.find((btn: any) => btn.name === "true")
            ?.id === edge.sourceHandle?.slice(24)
      );

      const falseButtonConnected = currentEdges.some(
        (edge) =>
          edge.source === node.id &&
          edge.sourceHandle &&
          edge.sourceHandle.includes("conditionButtonId-") &&
          node.data.conditionButtons.find((btn: any) => btn.name === "false")
            ?.id === edge.sourceHandle?.slice(24)
      );

      if (!trueButtonConnected || !falseButtonConnected) {
        validationErrors.push(
          `Condition Node: Both TRUE and FALSE paths must be connected to other nodes`
        );
        isValid = false;
      }
    }

    // Validate HttpRequest (Webhook) nodes
    if (node.type === NodeType.HttpRequest) {
      // Check if URL is provided
      if (!node.data.webhookTriggerUrl) {
        validationErrors.push(`Webhook Node: URL must be provided`);
        isValid = false;
      }

      // Check if HTTP method is selected
      if (!node.data.webhookTriggerHttpMethod) {
        validationErrors.push(`Webhook Node: HTTP method must be selected`);
        isValid = false;
      }

      // Check if body is valid JSON
      try {
        if (node.data.webhookTriggerBody) {
          JSON.parse(node.data.webhookTriggerBody);
        }
      } catch (error) {
        validationErrors.push(`Webhook Node: Body must be valid JSON`);
        isValid = false;
      }

      // Check if variables have all required fields
      if (node.data.variables && node.data.variables.webhookBodyVariables) {
        const invalidVariables =
          node.data.variables.webhookBodyVariables.filter(
            (prop: any) => !prop.veriable || !prop.value || !prop.fallbackValue
          );
        if (invalidVariables.length > 0) {
          validationErrors.push(
            `Webhook Node: All webhook variables must have variable, value, and fallback value`
          );
          isValid = false;
        }
      }
    }

    // Validate MediaType nodes
    if (node.type === NodeType.MediaType) {
      // 1. Media Type validation
      if (node.data.selectedMediaType !== 1) {
        // Not "None"
        if (node.data.selectedMediaType === 2 && !node.data.mediaCaption) {
          // Text type requires caption
          validationErrors.push("Media Type: Text header value is required");
          isValid = false;
        } else if (node.data.selectedMediaType === 3 && !node.data.mediaUrl) {
          // Image type requires URL
          validationErrors.push("Media Type: Image file is required");
          isValid = false;
        } else if (node.data.selectedMediaType === 4 && !node.data.mediaUrl) {
          // Video type requires URL
          validationErrors.push("Media Type: Video file is required");
          isValid = false;
        } else if (node.data.selectedMediaType === 5 && !node.data.mediaUrl) {
          // Document type requires URL
          validationErrors.push("Media Type: Document file is required");
          isValid = false;
        }
      }

      // 2. Message body validation
      if (!node.data.message || node.data.message.trim() === "") {
        validationErrors.push("Media Type: Message content cannot be empty");
        isValid = false;
      }

      // 3. Variables validation
      if (
        node.data.variables?.customMessageVariables?.normalVariables?.length >
          0 ||
        node.data.variables?.customMessageVariables?.leadratVariables?.length >
          0
      ) {
        const invalidNormalVariables =
          node.data.variables.customMessageVariables.normalVariables.filter(
            (variable: any) => !variable.value || !variable.fallbackValue
          );
        if (invalidNormalVariables.length > 0) {
          validationErrors.push(
            "Media Type: All normal variables must have value and fallback value"
          );
          isValid = false;
        }
        const invalidLeadratVariables =
          node.data.variables.customMessageVariables.leadratVariables.filter(
            (variable: any) => !variable.value || !variable.fallbackValue
          );
        if (invalidLeadratVariables.length > 0) {
          validationErrors.push(
            "Media Type: All leadrat variables must have value and fallback value"
          );
          isValid = false;
        }
      }

      // 4. Button validation
      if (!node.data.buttons || node.data.buttons.length === 0) {
        validationErrors.push("Media Type: At least one button is required");
        isValid = false;
      } else if (node.data.buttons.length > 3) {
        validationErrors.push("Media Type: Maximum 3 buttons are allowed");
        isValid = false;
      } else {
        // Check if all buttons have text
        const emptyButtons = node.data.buttons.filter(
          (button: any) => !button.text || button.text.trim() === ""
        );
        if (emptyButtons.length > 0) {
          validationErrors.push("Media Type: All buttons must have text");
          isValid = false;
        }

        // Check button connections
        const connectedButtons = currentEdges.filter(
          (edge) =>
            edge.source === node.id &&
            edge.sourceHandle &&
            edge.sourceHandle.startsWith("right-buttonId-")
        );
        const isNodeDirectlyConnected = currentEdges.some(
          (edge) =>
            edge.source === node.id &&
            edge.sourceHandle === `right-nodeId-${node.id}`
        );

        if (
          connectedButtons.length < node.data.buttons.length &&
          !isNodeDirectlyConnected
        ) {
          validationErrors.push(
            "Media Type: Either connect all buttons to other nodes or connect this node directly"
          );
          isValid = false;
        }
      }
    }

    // Validate Reminder nodes
    if (node.type === NodeType.Reminder) {
      // Check if template is selected
      if (!node.data.templateObj || !node.data.templateObj.templateId) {
        validationErrors.push("Reminder Node: A template must be selected");
        isValid = false;
      }

      // Check if reminder time is valid
      if (!node.data.reminderTime || node.data.reminderTime <= 0) {
        validationErrors.push(
          "Reminder Node: Reminder time must be greater than 0"
        );
        isValid = false;
      }

      // Check if reminder unit is selected
      if (
        !node.data.reminderUnit ||
        !["minutes", "hours", "days"].includes(node.data.reminderUnit)
      ) {
        validationErrors.push(
          "Reminder Node: A valid time unit must be selected"
        );
        isValid = false;
      }
    }

    return { isValid, validationErrors };
  };

  // Validate all nodes in the workflow
  const validateNodes = () => {
    let isValid = true;
    const invalidNodeIds: any = [];
    const validationErrors = [];

    // Check each node for validation errors
    for (const node of nodes) {
      // Skip validation for FlowStartNode
      if (node.type === NodeType.FlowStart) {
        continue;
      }

      const { isValid: nodeIsValid, validationErrors: nodeErrors } =
        validateSingleNode(node.id, edges);

      if (!nodeIsValid) {
        invalidNodeIds.push(node.id);
        validationErrors.push(...nodeErrors);
        isValid = false;
      }
    }

    // Update node validation states
    setNodes((prevNodes) =>
      prevNodes.map((node) => ({
        ...node,
        data: {
          ...node.data,
          isValid: !invalidNodeIds.includes(node.id),
        },
      }))
    );

    return { isValid, validationErrors };
  };

  const handleSave = async () => {
    setIsSavingWorkflow(true);

    // Validate all nodes
    const { isValid } = validateNodes();

    if (!isValid) {
      // Use a simple error message
      const errorMessage = "Validation Error, Check the Invalid nodes";

      dispatch(
        toastActions.setToaster({
          message: errorMessage,
          type: "error",
        })
      );
      setIsSavingWorkflow(false);
      return;
    }

    const formattedNodes = nodes?.map((node) =>
      createNode(node, node.type as NodeType, node.position.x, node.position.y)
    );

    const formattedEdges = edges?.map((edge) =>
      createEdge(edge, formattedNodes)
    );

    const cleanedNodes = formattedNodes?.map((node) => cleanObject(node));

    const cleanedEdges = formattedEdges?.map((edge) => cleanObject(edge));

    const payload = {
      id: id,

      data: {
        Name: tempWorkflowName || "default name",
        isActive: isWorkflowActive,
        Nodes: cleanedNodes,
        Edges: cleanedEdges,
      },
    };

    let response;
    try {
      response = await dispatch(updateWorkflowReactflow(payload));

      if (response?.meta?.requestStatus === "fulfilled") {
        const companyId = userData?.companyId;

        // Clear local state
        setNodes([]);
        setEdges([]);
        setTempWorkflowName("");

        const payload = {
          keywords: nodes.find((node: any) => node.type === NodeType.FlowStart)
            ?.data?.keywords,
          workflowId: id,
          workflowNodeId: nodes.find(
            (node: any) => node.type === NodeType.FlowStart
          )?.id,
        };
        await dispatch(createKeywordReactflow(payload));
        dispatch(
          toastActions.setToaster({
            message:
              response?.payload?.message || "Workflow updated successfully",
            type: "success",
          })
        );
        // Fetch the updated workflow data before navigating
        dispatch(getAllWorkflowsReactflow(companyId));
        navigate("/automation/workflows");
      } else {
        dispatch(
          toastActions.setToaster({
            message:
              response?.payload?.data?.message || "Failed to update workflow",
            type: "error",
          })
        );
        setIsSavingWorkflow(false);
      }
    } catch (error) {
      setIsSavingWorkflow(false);
      dispatch(
        toastActions.setToaster({
          message: "Failed to update workflow",
          type: "error",
        })
      );
    } finally {
      setIsSavingWorkflow(false);
    }
  };

  const handleDiscardChangesBtn = () => {
    setIsCancelPopupOpen(false);
    navigate("/automation/workflows");
  };
  const hanldeSaveChangesBtn = () => {
    setIsCancelPopupOpen(false);
    handleSave();
  };
  function handlePopupClose() {
    setIsCancelPopupOpen(false);
  }

  // Function to add a node when clicked in the sidebar
  const handleNodeClick = (item: any) => {
    // Find the rightmost node position
    let position = { x: 100, y: 100 }; // Default position if no nodes exist

    if (nodes.length > 0) {
      // Find the rightmost node
      let rightmostNode = nodes[0];
      nodes.forEach((node) => {
        if (node.position.x > rightmostNode.position.x) {
          rightmostNode = node;
        }
      });

      // Position new node 400px to the right and 100px below the rightmost node
      position = {
        x: rightmostNode.position.x + 650,
        y: rightmostNode.position.y + 100,
      };
    }

    const selectedAction = item;

    if (selectedAction) {
      const newNode: Node = {
        id: uuidv4(),
        type: selectedAction?.nodeType ?? "default",
        position,
        data: {
          keywords: [],
          leadSource: [],
          statusChange: [],
          triggerType: "keywords" as "keywords" | "newLead" | "statusChange",
          conditionType: [ConditionOperator.Equals],
          conditionButtons: [
            { id: uuidv4(), name: "true" },
            { id: uuidv4(), name: "false" },
          ],
          attribute: "",
          conditionValue: "",
          templateObj: null,
          selectedTemplate: null,
          selectedPath: true,
          selectedAction: selectedAction,
          isMessagePanelOpen: true,
          message: "",
          selectedButtonValue: selectedAction.value,
          buttons:
            selectedAction.value === InteractiveType.Button
              ? [
                  {
                    id: uuidv4(),
                    text: "",
                  },
                ]
              : [],
          selectedList: null,
          showSaveUserResponse: false,
          variables: {
            customMessageVariables: [] as any[],
            webhookBodyVariables: [] as any[],
          },
          saveResponseType: "variable",
          newVariableName: "",
          response: "",
          selectedUserResponse: null,
          editSelectResponseId: null,
          deleteSelectResponseId: "",
          webhookTriggerHttpMethod: "",
          webhookTriggerUrl: "www.example.com",
          webhookTriggerHeader: [
            { key: "Content-Type", value: "application/json" },
          ],
          defaultErrorResponse:
            "We are sorry. Unable to process your request at this time. Please try again later.",
          webhookTriggerBody: "{}",
          regards: "",
          // MediaType specific fields
          selectedMediaType: 1, // Default to NONE
          mediaUrl: "",
          mediaCaption: "",
          footer: "",
          // Reminder specific fields
          reminderTime: 1,
          reminderUnit: "minutes",
          timeoutTime: 1,
          timeoutUnit: "minutes",
          timeoutTimeInMinutes: 1,
          isEnableSetTimeOut: false,
          isSaved: false,
        },
      };
      setNodes((prevNodes: any) => [...prevNodes, newNode]);

      // Wait for the node to be rendered
      setTimeout(() => {
        // Get the current viewport
        const viewport = getViewport();

        // Calculate a wider view to include the sidebar
        const extendedView = {
          x: viewport.x - 200, // Adjusted to show more of the sidebar
          y: viewport.y,
          zoom: 0.7, // Set a consistent zoom level
        };

        // First set the view to include the sidebar
        setViewport(extendedView, { duration: 0 });

        // Then fit the view to the node with adjusted padding
        fitView({
          nodes: [newNode],
          duration: 800,
          padding: 0.4, // Increased padding
          minZoom: 0.3,
          maxZoom: 0.8, // Reduced max zoom
          includeHiddenNodes: true,
        });
      }, 300);
    }
  };

  return (
    <Box
      ref={containerRef}
      sx={{
        height: "100vh",
        width: "100%",
        position: "relative",
        overflow: "hidden",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <WorkflowProvider setNodes={setNodes}>
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          panOnDrag={true}
          zoomOnScroll={true}
          zoomOnPinch={true}
          minZoom={0.2}
          maxZoom={1.4}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          onConnect={onConnect}
          onDragOver={onDragOver}
          onDrop={onDrop}
          onReconnectStart={onReconnectStart}
          onReconnect={onReconnect}
          onReconnectEnd={onReconnectEnd}
          isValidConnection={isValidConnection}
          connectionLineComponent={ConnectionLine}
          connectionLineStyle={{ stroke: "grey", strokeWidth: 2 }}
          connectionLineType={ConnectionLineType.SmoothStep}
          onNodesDelete={(nodesToDelete) => {
            nodesToDelete.forEach((node) => handleNodeDelete(node.id));
          }}
          fitView
          style={{
            width: "100%",
            height: "100%",
            background: "#f8f8f8",
          }}
        >
          <Panel position="top-right" style={{ margin: "10px" }}>
            <Box
              sx={{
                backgroundColor: "white",
                borderRadius: "8px",
                padding: "8px",
                boxShadow: 1,
                border: "1px solid #e0e0e0",
              }}
            >
              <FormControlLabel
                control={
                  <Switch
                    checked={isWorkflowActive}
                    onChange={(e) => setIsWorkflowActive(e.target.checked)}
                    sx={{
                      "& .MuiSwitch-switchBase.Mui-checked": {
                        color: "#4caf50",
                        "&:hover": {
                          backgroundColor: "rgba(76, 175, 80, 0.04)",
                        },
                      },
                      "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track":
                        {
                          backgroundColor: "#4caf50",
                        },
                    }}
                    size="small"
                  />
                }
                label={
                  <Typography variant="caption">
                    {isWorkflowActive ? "Active" : "Inactive"}
                  </Typography>
                }
                labelPlacement="start"
                sx={{ margin: 0 }}
              />
            </Box>
          </Panel>
          <FlowSidebar
            ref={sidebarRef}
            onToggle={toggleArrow}
            isCollapsed={actionsVisible}
            messages={messages}
            actions={actions}
            onDragStart={onDragStart}
            onNodeClick={handleNodeClick}
            draggable={true}
            onSave={handleSave}
            tempWorkflowName={tempWorkflowName}
            setTempWorkflowName={setTempWorkflowName}
            onCancel={() => setIsCancelPopupOpen(true)}
            workflowData={workflowData}
            isWorkflowActive={isWorkflowActive}
            isSavingWorkflow={isSavingWorkflow}
            nodes={nodes}
          />
          <CancelDialogPopup
            open={isCancelPopupOpen}
            handleSave={hanldeSaveChangesBtn}
            handleDiscardChanges={handleDiscardChangesBtn}
            handleClose={handlePopupClose}
            title={tempWorkflowName}
          />
          <Controls
            position="bottom-right"
            style={{
              margin: "10px",
              zIndex: 10,
            }}
          />
          <Background />
        </ReactFlow>
      </WorkflowProvider>
    </Box>
  );
};

export default WorkflowReactFlow;
