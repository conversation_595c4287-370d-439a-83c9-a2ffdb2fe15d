/**
 * Rate Limiting Utility for SignalR Contact API Calls
 * 
 * This utility manages rate limiting for contact API calls during campaigns
 * and persists the block state in localStorage to survive page refreshes.
 */

interface RateLimitState {
  callTimestamps: number[];
  isBlocked: boolean;
  blockStartTime: number | null;
}

export class ContactAPIRateLimiter {
  private readonly RATE_LIMIT_WINDOW = 10000; // 10 seconds
  private readonly MAX_CALLS = 50;
  private readonly BLOCK_DURATION = 5 * 60 * 1000; // 5 minutes
  private readonly STORAGE_KEY = 'contact_api_rate_limit_state';

  /**
   * Gets the current rate limit state from localStorage or returns default state
   */
  private getState(): RateLimitState {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        return {
          callTimestamps: parsed.callTimestamps || [],
          isBlocked: parsed.isBlocked || false,
          blockStartTime: parsed.blockStartTime || null,
        };
      }
    } catch (error) {
      console.warn('[Rate Limiter] Error reading from localStorage:', error);
    }

    return {
      callTimestamps: [],
      isBlocked: false,
      blockStartTime: null,
    };
  }

  /**
   * Saves the current rate limit state to localStorage
   */
  private saveState(state: RateLimitState): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(state));
    } catch (error) {
      console.warn('[Rate Limiter] Error saving to localStorage:', error);
    }
  }

  /**
   * Checks if the current API call should be allowed based on rate limiting rules
   * Returns true if the call should be allowed, false if it should be blocked
   */
  public checkRateLimit(): boolean {
    const now = Date.now();
    const state = this.getState();

    // Check if currently blocked
    if (state.isBlocked && state.blockStartTime) {
      const timeSinceBlock = now - state.blockStartTime;
      if (timeSinceBlock < this.BLOCK_DURATION) {
        const remainingTime = Math.ceil((this.BLOCK_DURATION - timeSinceBlock) / 1000);
        console.warn(
          `[Rate Limiter] Blocked for ${remainingTime} more seconds`
        );
        return false; // Still blocked
      } else {
        // Block period expired, reset state
        const resetState: RateLimitState = {
          callTimestamps: [],
          isBlocked: false,
          blockStartTime: null,
        };
        this.saveState(resetState);
        console.log(
          "[Rate Limiter] Block period expired, resuming normal operation"
        );
      }
    }

    // Add current timestamp
    const updatedTimestamps = [...state.callTimestamps, now];

    // Remove timestamps older than the rate limit window
    const recentTimestamps = updatedTimestamps.filter(
      (timestamp) => now - timestamp <= this.RATE_LIMIT_WINDOW
    );

    // Check if rate limit exceeded
    if (recentTimestamps.length > this.MAX_CALLS) {
      console.warn(
        `[Rate Limiter] Exceeded ${this.MAX_CALLS} calls in ${
          this.RATE_LIMIT_WINDOW / 1000
        } seconds. Blocking for ${this.BLOCK_DURATION / 1000 / 60} minutes.`
      );
      
      const blockedState: RateLimitState = {
        callTimestamps: [],
        isBlocked: true,
        blockStartTime: now,
      };
      this.saveState(blockedState);
      return false; // Block this call
    }

    // Update state with recent timestamps
    const updatedState: RateLimitState = {
      callTimestamps: recentTimestamps,
      isBlocked: false,
      blockStartTime: null,
    };
    this.saveState(updatedState);

    // Log current rate limit status for debugging
    if (recentTimestamps.length > 25) {
      // Log when approaching limit (half of max calls)
      console.log(
        `[Rate Limiter] Current calls in window: ${recentTimestamps.length}/${this.MAX_CALLS}`
      );
    }

    return true; // Allow this call
  }

  /**
   * Gets the current block status and remaining time
   * Returns null if not blocked, or an object with block info if blocked
   */
  public getBlockStatus(): { isBlocked: boolean; remainingMinutes?: number } {
    const state = this.getState();
    
    if (!state.isBlocked || !state.blockStartTime) {
      return { isBlocked: false };
    }

    const now = Date.now();
    const timeSinceBlock = now - state.blockStartTime;
    
    if (timeSinceBlock >= this.BLOCK_DURATION) {
      // Block has expired, clean up state
      const resetState: RateLimitState = {
        callTimestamps: [],
        isBlocked: false,
        blockStartTime: null,
      };
      this.saveState(resetState);
      return { isBlocked: false };
    }

    const remainingTime = this.BLOCK_DURATION - timeSinceBlock;
    const remainingMinutes = Math.ceil(remainingTime / 1000 / 60);

    return {
      isBlocked: true,
      remainingMinutes,
    };
  }

  /**
   * Manually resets the rate limiter state (for testing or admin purposes)
   */
  public reset(): void {
    const resetState: RateLimitState = {
      callTimestamps: [],
      isBlocked: false,
      blockStartTime: null,
    };
    this.saveState(resetState);
    console.log("[Rate Limiter] State manually reset");
  }

  /**
   * Gets current call count in the rate limit window (for debugging)
   */
  public getCurrentCallCount(): number {
    const now = Date.now();
    const state = this.getState();
    
    const recentTimestamps = state.callTimestamps.filter(
      (timestamp) => now - timestamp <= this.RATE_LIMIT_WINDOW
    );
    
    return recentTimestamps.length;
  }
}

// Export a singleton instance
export const contactAPIRateLimiter = new ContactAPIRateLimiter();
