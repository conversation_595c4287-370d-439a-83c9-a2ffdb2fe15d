/**
 * Rate Limiting Test Utility
 * 
 * This utility can be used to test the rate limiting functionality
 * implemented in the Home component for SignalR message handling.
 */

interface RateLimitState {
  callTimestamps: number[];
  isBlocked: boolean;
  blockStartTime: number | null;
}

export class RateLimitTester {
  private state: RateLimitState = {
    callTimestamps: [],
    isBlocked: false,
    blockStartTime: null,
  };

  private readonly RATE_LIMIT_WINDOW = 5000; // 5 seconds
  private readonly MAX_CALLS = 100;
  private readonly BLOCK_DURATION = 10 * 60 * 1000; // 10 minutes

  /**
   * Simulates the rate limiting check logic from the Home component
   */
  checkRateLimit(): boolean {
    const now = Date.now();

    // Check if currently blocked
    if (this.state.isBlocked && this.state.blockStartTime) {
      const timeSinceBlock = now - this.state.blockStartTime;
      if (timeSinceBlock < this.BLOCK_DURATION) {
        console.warn(
          `[Rate Limit Test] Blocked for ${Math.ceil(
            (this.BLOCK_DURATION - timeSinceBlock) / 1000
          )} more seconds`
        );
        return false; // Still blocked
      } else {
        // Block period expired, reset state
        this.state = {
          callTimestamps: [],
          isBlocked: false,
          blockStartTime: null,
        };
        console.log(
          "[Rate Limit Test] Block period expired, resuming normal operation"
        );
      }
    }

    // Add current timestamp
    const updatedTimestamps = [...this.state.callTimestamps, now];

    // Remove timestamps older than the rate limit window
    const recentTimestamps = updatedTimestamps.filter(
      (timestamp) => now - timestamp <= this.RATE_LIMIT_WINDOW
    );

    // Check if rate limit exceeded
    if (recentTimestamps.length > this.MAX_CALLS) {
      console.warn(
        `[Rate Limit Test] Exceeded ${this.MAX_CALLS} calls in ${
          this.RATE_LIMIT_WINDOW / 1000
        } seconds. Blocking for ${this.BLOCK_DURATION / 1000 / 60} minutes.`
      );
      this.state = {
        callTimestamps: [],
        isBlocked: true,
        blockStartTime: now,
      };
      return false; // Block this call
    }

    // Update state with recent timestamps
    this.state = {
      ...this.state,
      callTimestamps: recentTimestamps,
    };

    // Log current rate limit status for debugging
    if (recentTimestamps.length > 50) {
      console.log(
        `[Rate Limit Test] Current calls in window: ${recentTimestamps.length}/${this.MAX_CALLS}`
      );
    }

    return true; // Allow this call
  }

  /**
   * Simulates rapid SignalR calls to test rate limiting
   */
  async simulateRapidCalls(numberOfCalls: number, delayMs: number = 10): Promise<void> {
    console.log(`[Rate Limit Test] Starting simulation of ${numberOfCalls} calls with ${delayMs}ms delay`);
    
    let allowedCalls = 0;
    let blockedCalls = 0;

    for (let i = 0; i < numberOfCalls; i++) {
      const isAllowed = this.checkRateLimit();
      
      if (isAllowed) {
        allowedCalls++;
        console.log(`[Rate Limit Test] Call ${i + 1}: ALLOWED (Total allowed: ${allowedCalls})`);
      } else {
        blockedCalls++;
        console.log(`[Rate Limit Test] Call ${i + 1}: BLOCKED (Total blocked: ${blockedCalls})`);
      }

      // Add small delay between calls
      if (delayMs > 0) {
        await new Promise(resolve => setTimeout(resolve, delayMs));
      }
    }

    console.log(`[Rate Limit Test] Simulation complete. Allowed: ${allowedCalls}, Blocked: ${blockedCalls}`);
  }

  /**
   * Gets current state for debugging
   */
  getState(): RateLimitState {
    return { ...this.state };
  }

  /**
   * Resets the rate limiter state
   */
  reset(): void {
    this.state = {
      callTimestamps: [],
      isBlocked: false,
      blockStartTime: null,
    };
    console.log("[Rate Limit Test] State reset");
  }
}

// Example usage:
// const tester = new RateLimitTester();
// tester.simulateRapidCalls(150, 20); // Simulate 150 calls with 20ms delay
